import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import json
from sqlalchemy.orm import Session

from app.models.pydantic_models import ImportPreviewResponse, ImportPreviewRow
from app.models.database_models import Branch, Category, Product, Design, SubDesign

class FileProcessor:
    def __init__(self, db: Session):
        self.db = db
        self.required_columns = [
            'tag_no', 'branch_name', 'category_name', 'product_name'
        ]
        self.optional_columns = [
            'design_name', 'sub_design_name', 'gross_weight', 'net_weight',
            'stone_weight', 'purity', 'making_charge', 'stone_charge',
            'other_charge', 'total_amount'
        ]
        
    async def preview_file(self, file_path: Path, max_rows: int = 10) -> ImportPreviewResponse:
        """Preview the uploaded file and validate data"""
        
        # Read file based on extension
        try:
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_path.suffix}")
        except Exception as e:
            raise ValueError(f"Failed to read file: {str(e)}")
        
        # Basic validation
        if df.empty:
            raise ValueError("File is empty")
        
        # Clean column names (remove spaces, convert to lowercase)
        df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_')
        
        # Check for required columns
        missing_columns = []
        for col in self.required_columns:
            if col not in df.columns:
                missing_columns.append(col)
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Get reference data for validation
        branches = {b.name.lower(): b.id_branch for b in self.db.query(Branch).filter(Branch.active == True).all()}
        categories = {c.categoryname.lower(): c.id_category for c in self.db.query(Category).filter(Category.active == True).all()}
        products = {p.productname.lower(): p.id_product for p in self.db.query(Product).filter(Product.active == True).all()}
        designs = {d.designname.lower(): d.id_design for d in self.db.query(Design).filter(Design.active == True).all()}
        sub_designs = {sd.sub_designname.lower(): sd.id_sub_design for sd in self.db.query(SubDesign).filter(SubDesign.active == True).all()}
        
        # Process preview rows
        preview_rows = []
        valid_rows = 0
        error_rows = 0
        
        for idx, row in df.head(max_rows).iterrows():
            row_data = row.to_dict()
            errors = []
            warnings = []
            
            # Validate required fields
            if pd.isna(row.get('tag_no')) or str(row.get('tag_no')).strip() == '':
                errors.append("Tag number is required")
            
            # Validate branch
            branch_name = str(row.get('branch_name', '')).strip().lower()
            if branch_name and branch_name not in branches:
                errors.append(f"Branch '{row.get('branch_name')}' not found")
            
            # Validate category
            category_name = str(row.get('category_name', '')).strip().lower()
            if category_name and category_name not in categories:
                errors.append(f"Category '{row.get('category_name')}' not found")
            
            # Validate product
            product_name = str(row.get('product_name', '')).strip().lower()
            if product_name and product_name not in products:
                errors.append(f"Product '{row.get('product_name')}' not found")
            
            # Validate design (optional)
            design_name = str(row.get('design_name', '')).strip().lower()
            if design_name and design_name not in designs:
                warnings.append(f"Design '{row.get('design_name')}' not found")
            
            # Validate sub-design (optional)
            sub_design_name = str(row.get('sub_design_name', '')).strip().lower()
            if sub_design_name and sub_design_name not in sub_designs:
                warnings.append(f"Sub-design '{row.get('sub_design_name')}' not found")
            
            # Validate numeric fields
            numeric_fields = ['gross_weight', 'net_weight', 'stone_weight', 'purity', 
                            'making_charge', 'stone_charge', 'other_charge', 'total_amount']
            
            for field in numeric_fields:
                if field in row_data and not pd.isna(row_data[field]):
                    try:
                        float(row_data[field])
                    except (ValueError, TypeError):
                        errors.append(f"Invalid numeric value for {field}: {row_data[field]}")
            
            # Count valid/error rows
            if errors:
                error_rows += 1
            else:
                valid_rows += 1
            
            preview_rows.append(ImportPreviewRow(
                row_number=idx + 1,
                data=row_data,
                errors=errors,
                warnings=warnings
            ))
        
        # Create column mapping
        column_mapping = {}
        for col in df.columns:
            if col in self.required_columns + self.optional_columns:
                column_mapping[col] = col
        
        return ImportPreviewResponse(
            total_rows=len(df),
            valid_rows=valid_rows,
            error_rows=error_rows,
            preview_data=preview_rows,
            column_mapping=column_mapping
        )
    
    def validate_row(self, row_data: Dict[str, Any]) -> List[str]:
        """Validate a single row of data"""
        errors = []
        
        # Check required fields
        if not row_data.get('tag_no'):
            errors.append("Tag number is required")
        
        if not row_data.get('branch_name'):
            errors.append("Branch name is required")
        
        if not row_data.get('category_name'):
            errors.append("Category name is required")
        
        if not row_data.get('product_name'):
            errors.append("Product name is required")
        
        return errors
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize the data"""
        # Clean column names
        df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_')
        
        # Clean string fields
        string_fields = ['tag_no', 'branch_name', 'category_name', 'product_name', 
                        'design_name', 'sub_design_name']
        
        for field in string_fields:
            if field in df.columns:
                df[field] = df[field].astype(str).str.strip()
                df[field] = df[field].replace('nan', '')
        
        # Clean numeric fields
        numeric_fields = ['gross_weight', 'net_weight', 'stone_weight', 'purity',
                         'making_charge', 'stone_charge', 'other_charge', 'total_amount']
        
        for field in numeric_fields:
            if field in df.columns:
                df[field] = pd.to_numeric(df[field], errors='coerce')
        
        return df
