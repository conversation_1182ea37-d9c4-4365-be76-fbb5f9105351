"""
Field mapping configurations for Excel import
Maps user-friendly Excel headers to database field names
Based on tag_import.xlsx structure analysis
"""

from typing import Dict, List, Any
from enum import Enum


class SheetType(Enum):
    """Enum for different sheet types in the Excel file"""
    CATEGORY = "category"
    PRODUCT = "product"
    DESIGN = "design"
    SUB_DESIGN = "sub_design"
    DESIGN_MAP = "design_map"
    SUB_DESIGN_MAP = "sub_design map"
    PRODUCT_SECTION_MAP = "product_section map"
    TAG_DETAILS = "tag_details"
    TAG_STONE_DETAILS = "tag_stone_details"
    TAG_CHARGES = "tag_charges"


# Field mappings for each sheet type
FIELD_MAPPINGS: Dict[str, Dict[str, str]] = {
    
    # Category sheet mapping (12 columns)
    "category": {
        "Category Name": "categoryname",
        "HSN Code": "hsn_code",
        "Short Code": "short_code",
        "Category Type": "category_type",
        "Is Multi_metal": "is_multi_metal",
        "Metal ID": "metal_id",
        "Metal": "metal_name",
        "Tax Group ID ": "tax_group_id",  # Note: space in Excel header
        "Tax Group ID": "tax_group_id",   # Alternative without space
        "Tax Group": "tax_group_name",
        "Purity ID": "purity_id",
        "Purity": "purity_name",
        "Description": "description"
    },
    
    # Product sheet mapping (19 columns)
    "product": {
        "Product Name": "productname",
        "Short Code": "short_code",
        "HSN Code": "hsn_code",
        "Metal ID": "metal_id",
        "Metal": "metal_name",
        "Category ID": "id_category",
        "Category Name": "category_name",
        "Tax Type": "tax_type",
        "Pur Tax Group ID": "pur_tax_group_id",
        "Pur Tax Group": "pur_tax_group_name",
        "No. Of Pieces": "no_of_pieces",
        "Stock Type": "stock_type",
        "Product Type": "product_type",
        "UoM ID": "uom_id",
        "UoM": "uom_name",
        "Purchase Based On": "purchase_based_on",
        "Sales Based On": "sales_based_on",
        "Re_Order Based On": "reorder_based_on",
        "Calculation Based On": "calculation_based_on"
    },
    
    # Design sheet mapping (2 columns)
    "design": {
        "Design ID": "id_design",
        "Design Name": "designname"
    },
    
    # Sub Design sheet mapping (2 columns)
    "sub_design": {
        "Sub Design ID": "id_sub_design",
        "Sub Design Name": "sub_designname"
    },
    
    # Design Map sheet mapping (4 columns)
    "design_map": {
        "Product ID": "id_product",
        "Product Name": "product_name",
        "Design ID": "id_design",
        "Design Name": "design_name"
    },
    
    # Sub Design Map sheet mapping (6 columns)
    "sub_design map": {
        "Product ID": "id_product",
        "Product Name": "product_name",
        "Design ID": "id_design",
        "Design Name": "design_name",
        "Sub Design ID": "id_sub_design",
        "Sub Design Name": "sub_design_name"
    },
    
    # Product Section Map sheet mapping (4 columns)
    "product_section map": {
        "Product ID": "id_product",
        "Product Name": "product_name",
        "Section ID": "section_id",
        "Section Name": "section_name"
    }
}


# Required fields for each sheet type
REQUIRED_FIELDS: Dict[str, List[str]] = {
    "category": ["categoryname"],
    "product": ["productname", "id_category"],
    "design": ["designname"],
    "sub_design": ["sub_designname"],
    "design_map": ["id_product", "id_design"],
    "sub_design map": ["id_design", "id_sub_design"],
    "product_section map": ["id_product", "section_id"]
}


# Field validation rules
FIELD_VALIDATIONS: Dict[str, Dict[str, Dict[str, Any]]] = {
    "category": {
        "categoryname": {
            "max_length": 100,
            "required": True,
            "unique": True
        },
        "hsn_code": {
            "max_length": 20,
            "pattern": r"^[0-9]{4,8}$"  # HSN codes are typically 4-8 digits
        },
        "short_code": {
            "max_length": 10
        },
        "is_multi_metal": {
            "type": "boolean",
            "valid_values": [True, False, "Y", "N", "Yes", "No", 1, 0]
        }
    },
    
    "product": {
        "productname": {
            "max_length": 100,
            "required": True
        },
        "id_category": {
            "type": "integer",
            "required": True,
            "foreign_key": "category.id_category"
        },
        "no_of_pieces": {
            "type": "integer",
            "min_value": 1,
            "default": 1
        }
    },
    
    "design": {
        "designname": {
            "max_length": 100,
            "required": True,
            "unique": True
        }
    },
    
    "sub_design": {
        "sub_designname": {
            "max_length": 100,
            "required": True,
            "unique": True
        }
    }
}


# Import sequence - order matters for foreign key dependencies
IMPORT_SEQUENCE: List[str] = [
    "category",      # Independent
    "design",        # Independent
    "sub_design",    # Independent
    "product",       # Depends on category
    "design_map",    # Depends on product and design
    "sub_design map", # Depends on design and sub_design
    "product_section map"  # Depends on product
]


def get_field_mapping(sheet_name: str) -> Dict[str, str]:
    """Get field mapping for a specific sheet"""
    return FIELD_MAPPINGS.get(sheet_name.lower(), {})


def get_required_fields(sheet_name: str) -> List[str]:
    """Get required fields for a specific sheet"""
    return REQUIRED_FIELDS.get(sheet_name.lower(), [])


def get_field_validations(sheet_name: str) -> Dict[str, Dict[str, Any]]:
    """Get field validation rules for a specific sheet"""
    return FIELD_VALIDATIONS.get(sheet_name.lower(), {})


def is_valid_sheet(sheet_name: str) -> bool:
    """Check if sheet name is valid for import"""
    return sheet_name.lower() in FIELD_MAPPINGS


def get_import_sequence() -> List[str]:
    """Get the correct import sequence for all sheets"""
    return IMPORT_SEQUENCE.copy()


def clean_header_name(header: str) -> str:
    """Clean Excel header name for mapping"""
    # Remove common comment patterns
    cleaned = header.strip()
    
    # Remove asterisks for required fields
    cleaned = cleaned.replace('*', '').strip()
    
    # Remove parenthetical comments
    if '(' in cleaned:
        cleaned = cleaned.split('(')[0].strip()
    
    # Remove brackets
    import re
    cleaned = re.sub(r'\[.*?\]', '', cleaned).strip()
    
    return cleaned


def auto_detect_mapping(excel_headers: List[str], sheet_name: str) -> Dict[str, str]:
    """Auto-detect field mapping based on header names"""
    mapping = get_field_mapping(sheet_name)
    detected_mapping = {}
    
    for excel_header in excel_headers:
        cleaned_header = clean_header_name(excel_header)
        
        # Direct match
        if cleaned_header in mapping:
            detected_mapping[excel_header] = mapping[cleaned_header]
        else:
            # Fuzzy matching for common variations
            for map_key, db_field in mapping.items():
                if cleaned_header.lower() == map_key.lower():
                    detected_mapping[excel_header] = db_field
                    break
    
    return detected_mapping
