import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const apiEndpoints = {
  // Health
  health: () => api.get('/health'),
  healthDatabase: () => api.get('/health/database'),
  
  // Upload
  uploadFile: (formData: FormData) => 
    api.post('/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  previewImport: (batchId: number, maxRows: number = 10) => 
    api.get(`/preview/${batchId}?max_rows=${maxRows}`),
  deleteUpload: (batchId: number) => api.delete(`/upload/${batchId}`),
  
  // Tags
  getBranches: (activeOnly: boolean = true) => 
    api.get(`/branches?active_only=${activeOnly}`),
  getCategories: (activeOnly: boolean = true) => 
    api.get(`/categories?active_only=${activeOnly}`),
  getProducts: (categoryId?: number, activeOnly: boolean = true) => 
    api.get(`/products?${categoryId ? `category_id=${categoryId}&` : ''}active_only=${activeOnly}`),
  getDesigns: (productId?: number, activeOnly: boolean = true) => 
    api.get(`/designs?${productId ? `product_id=${productId}&` : ''}active_only=${activeOnly}`),
  getSubDesigns: (designId?: number, activeOnly: boolean = true) => 
    api.get(`/sub-designs?${designId ? `design_id=${designId}&` : ''}active_only=${activeOnly}`),
  
  getTags: (params?: {
    branch_id?: number;
    category_id?: number;
    status?: number;
    limit?: number;
    offset?: number;
  }) => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    return api.get(`/tags?${queryParams.toString()}`);
  },
  
  getTag: (tagId: number) => api.get(`/tags/${tagId}`),
  getTagByNumber: (tagNo: string) => api.get(`/tags/by-number/${tagNo}`),
  
  // Import batches
  getImportBatches: (params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    return api.get(`/import-batches?${queryParams.toString()}`);
  },
  
  getImportBatch: (batchId: number) => api.get(`/import-batches/${batchId}`),
  getImportErrors: (batchId: number, limit: number = 100, offset: number = 0) => 
    api.get(`/import-batches/${batchId}/errors?limit=${limit}&offset=${offset}`),
  startImport: (batchId: number) => api.post(`/import-batches/${batchId}/start`),
  getImportProgress: (batchId: number) => api.get(`/import-batches/${batchId}/progress`),
};

export default api;
