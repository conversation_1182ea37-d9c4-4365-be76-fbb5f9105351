from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from decimal import Decimal

# Base models for existing entities
class BranchBase(BaseModel):
    name: str
    warehouse: Optional[str] = None
    active: bool = True

class BranchResponse(BranchBase):
    id_branch: int
    
    class Config:
        from_attributes = True

class CategoryBase(BaseModel):
    categoryname: str
    description: Optional[str] = None
    active: bool = True

class CategoryResponse(CategoryBase):
    id_category: int
    date_add: datetime
    
    class Config:
        from_attributes = True

class ProductBase(BaseModel):
    productname: str
    description: Optional[str] = None
    id_category: int
    active: bool = True

class ProductResponse(ProductBase):
    id_product: int
    date_add: datetime
    
    class Config:
        from_attributes = True

class DesignBase(BaseModel):
    designname: str
    description: Optional[str] = None
    id_product: int
    active: bool = True

class DesignResponse(DesignBase):
    id_design: int
    date_add: datetime
    
    class Config:
        from_attributes = True

class SubDesignBase(BaseModel):
    sub_designname: str
    description: Optional[str] = None
    id_design: int
    active: bool = True

class SubDesignResponse(SubDesignBase):
    id_sub_design: int
    date_add: datetime
    
    class Config:
        from_attributes = True

# Tag models
class TagCreate(BaseModel):
    tag_no: str = Field(..., description="Unique tag number")
    id_branch: int
    id_category: int
    id_product: int
    id_design: Optional[int] = None
    id_sub_design: Optional[int] = None
    gross_weight: Optional[Decimal] = None
    net_weight: Optional[Decimal] = None
    stone_weight: Optional[Decimal] = None
    purity: Optional[Decimal] = None
    making_charge: Optional[Decimal] = None
    stone_charge: Optional[Decimal] = None
    other_charge: Optional[Decimal] = None
    total_amount: Optional[Decimal] = None
    status: int = 1

class TagResponse(TagCreate):
    id_tag: int
    date_add: datetime
    date_upd: datetime
    
    class Config:
        from_attributes = True

# Import batch models
class ImportBatchCreate(BaseModel):
    batch_name: str
    file_name: str
    created_by: Optional[str] = None

class ImportBatchResponse(BaseModel):
    id_batch: int
    batch_name: str
    file_name: str
    total_rows: int
    processed_rows: int
    success_rows: int
    error_rows: int
    status: str
    created_by: Optional[str]
    created_at: datetime
    completed_at: Optional[datetime]
    error_message: Optional[str]
    
    class Config:
        from_attributes = True

class ImportErrorResponse(BaseModel):
    id_error: int
    row_number: int
    column_name: Optional[str]
    error_type: str
    error_message: str
    row_data: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# File upload models
class FileUploadResponse(BaseModel):
    filename: str
    file_size: int
    file_type: str
    upload_path: str
    batch_id: int

class ImportPreviewRow(BaseModel):
    row_number: int
    data: dict
    errors: List[str] = []
    warnings: List[str] = []

class ImportPreviewResponse(BaseModel):
    total_rows: int
    valid_rows: int
    error_rows: int
    preview_data: List[ImportPreviewRow]
    column_mapping: dict
    
# Import progress models
class ImportProgressResponse(BaseModel):
    batch_id: int
    status: str
    total_rows: int
    processed_rows: int
    success_rows: int
    error_rows: int
    progress_percentage: float
    current_operation: Optional[str] = None
    estimated_time_remaining: Optional[int] = None  # in seconds
