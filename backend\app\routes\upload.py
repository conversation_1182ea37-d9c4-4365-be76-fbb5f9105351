from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from sqlalchemy.orm import Session
from typing import Optional
import os
import uuid
from pathlib import Path
import pandas as pd

from app.database.connection import get_db
from app.models.pydantic_models import FileUploadResponse, ImportPreviewResponse
from app.models.database_models import TagImportBatch
from app.services.file_processor import FileProcessor
from app.config import settings

router = APIRouter()

@router.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    batch_name: str = Form(...),
    created_by: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """Upload a file for tag import"""
    
    # Validate file type
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in settings.allowed_file_types:
        raise HTTPException(
            status_code=400,
            detail=f"File type {file_extension} not allowed. Allowed types: {settings.allowed_file_types}"
        )
    
    # Validate file size
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    if file_size > settings.max_file_size:
        raise HTTPException(
            status_code=400,
            detail=f"File size {file_size} exceeds maximum allowed size {settings.max_file_size}"
        )
    
    # Generate unique filename
    unique_filename = f"{uuid.uuid4()}_{file.filename}"
    upload_path = Path(settings.upload_dir) / unique_filename
    
    # Ensure upload directory exists
    upload_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Save file
    try:
        with open(upload_path, "wb") as buffer:
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # Create import batch record
    try:
        batch = TagImportBatch(
            batch_name=batch_name,
            file_name=file.filename,
            file_path=str(upload_path),
            created_by=created_by
        )
        db.add(batch)
        db.commit()
        db.refresh(batch)
        
        return FileUploadResponse(
            filename=file.filename,
            file_size=file_size,
            file_type=file_extension,
            upload_path=str(upload_path),
            batch_id=batch.id_batch
        )
    except Exception as e:
        # Clean up uploaded file if database operation fails
        if upload_path.exists():
            upload_path.unlink()
        raise HTTPException(status_code=500, detail=f"Failed to create batch record: {str(e)}")

@router.get("/preview/{batch_id}", response_model=ImportPreviewResponse)
async def preview_import(
    batch_id: int,
    max_rows: int = 10,
    db: Session = Depends(get_db)
):
    """Preview the uploaded file data before import"""
    
    # Get batch record
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Batch not found")
    
    # Check if file exists
    file_path = Path(batch.file_path)
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Uploaded file not found")
    
    try:
        # Process file preview
        processor = FileProcessor(db)
        preview_data = await processor.preview_file(file_path, max_rows)
        
        return preview_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to preview file: {str(e)}")

@router.delete("/upload/{batch_id}")
async def delete_upload(batch_id: int, db: Session = Depends(get_db)):
    """Delete an uploaded file and its batch record"""
    
    # Get batch record
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Batch not found")
    
    # Delete file if it exists
    file_path = Path(batch.file_path)
    if file_path.exists():
        try:
            file_path.unlink()
        except Exception as e:
            print(f"Warning: Failed to delete file {file_path}: {e}")
    
    # Delete batch record
    try:
        db.delete(batch)
        db.commit()
        return {"message": "Upload deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete batch: {str(e)}")
