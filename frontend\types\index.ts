// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

// Entity Types
export interface Branch {
  id_branch: number;
  name: string;
  warehouse?: string;
  active: boolean;
  short_name?: string;
  email?: string;
  address1?: string;
  address2?: string;
  phone?: string;
  mobile?: string;
}

export interface Category {
  id_category: number;
  categoryname: string;
  description?: string;
  active: boolean;
  date_add: string;
}

export interface Product {
  id_product: number;
  productname: string;
  description?: string;
  id_category: number;
  active: boolean;
  date_add: string;
}

export interface Design {
  id_design: number;
  designname: string;
  description?: string;
  id_product: number;
  active: boolean;
  date_add: string;
}

export interface SubDesign {
  id_sub_design: number;
  sub_designname: string;
  description?: string;
  id_design: number;
  active: boolean;
  date_add: string;
}

export interface Tag {
  id_tag: number;
  tag_no: string;
  id_branch: number;
  id_category: number;
  id_product: number;
  id_design?: number;
  id_sub_design?: number;
  gross_weight?: number;
  net_weight?: number;
  stone_weight?: number;
  purity?: number;
  making_charge?: number;
  stone_charge?: number;
  other_charge?: number;
  total_amount?: number;
  status: number;
  date_add: string;
  date_upd: string;
}

// Import Types
export interface ImportBatch {
  id_batch: number;
  batch_name: string;
  file_name: string;
  total_rows: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  created_by?: string;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface ImportError {
  id_error: number;
  row_number: number;
  column_name?: string;
  error_type: string;
  error_message: string;
  row_data?: string;
  created_at: string;
}

export interface FileUploadResponse {
  filename: string;
  file_size: number;
  file_type: string;
  upload_path: string;
  batch_id: number;
}

export interface ImportPreviewRow {
  row_number: number;
  data: Record<string, any>;
  errors: string[];
  warnings: string[];
}

export interface ImportPreviewResponse {
  total_rows: number;
  valid_rows: number;
  error_rows: number;
  preview_data: ImportPreviewRow[];
  column_mapping: Record<string, string>;
}

export interface ImportProgressResponse {
  batch_id: number;
  status: string;
  total_rows: number;
  processed_rows: number;
  success_rows: number;
  error_rows: number;
  progress_percentage: number;
  current_operation?: string;
  estimated_time_remaining?: number;
}

// Form Types
export interface UploadFormData {
  file: File;
  batch_name: string;
  created_by?: string;
}

// Component Props Types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  render?: (value: any, item: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  totalItems: number;
}
