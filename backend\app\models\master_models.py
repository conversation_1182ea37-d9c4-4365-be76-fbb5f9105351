"""
SQLAlchemy models for jewelry master data
Based on the Excel structure from tag_import.xlsx
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database_models import Base


class Category(Base):
    """Category master table - matches 'category' sheet in Excel"""
    __tablename__ = "category"

    id_category = Column(Integer, primary_key=True, autoincrement=True)
    categoryname = Column(String(100), nullable=False, unique=True)
    hsn_code = Column(String(20))
    short_code = Column(String(10))
    category_type = Column(String(50))
    is_multi_metal = Column(Boolean, default=False)
    metal_id = Column(Integer)
    metal_name = Column(String(50))
    tax_group_id = Column(Integer)
    tax_group_name = Column(String(100))
    purity_id = Column(Integer)
    purity_name = Column(String(50))
    description = Column(Text)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())
    date_upd = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    products = relationship("Product", back_populates="category")


class Product(Base):
    """Product master table - matches 'product' sheet in Excel"""
    __tablename__ = "products"

    id_product = Column(Integer, primary_key=True, autoincrement=True)
    productname = Column(String(100), nullable=False)
    short_code = Column(String(10))
    hsn_code = Column(String(20))
    metal_id = Column(Integer)
    metal_name = Column(String(50))
    id_category = Column(Integer, ForeignKey('category.id_category'), nullable=False)
    category_name = Column(String(100))
    tax_type = Column(String(50))
    pur_tax_group_id = Column(Integer)
    pur_tax_group_name = Column(String(100))
    no_of_pieces = Column(Integer, default=1)
    stock_type = Column(String(50))
    product_type = Column(String(50))
    uom_id = Column(Integer)
    uom_name = Column(String(50))
    purchase_based_on = Column(String(50))
    sales_based_on = Column(String(50))
    reorder_based_on = Column(String(50))
    calculation_based_on = Column(String(50))
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())
    date_upd = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    category = relationship("Category", back_populates="products")
    design_maps = relationship("DesignMap", back_populates="product")
    section_maps = relationship("ProductSectionMap", back_populates="product")


class Design(Base):
    """Design master table - matches 'design' sheet in Excel"""
    __tablename__ = "ret_design_master"

    id_design = Column(Integer, primary_key=True, autoincrement=True)
    designname = Column(String(100), nullable=False, unique=True)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())
    date_upd = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    design_maps = relationship("DesignMap", back_populates="design")
    sub_design_maps = relationship("SubDesignMap", back_populates="design")


class SubDesign(Base):
    """Sub Design master table - matches 'sub_design' sheet in Excel"""
    __tablename__ = "ret_sub_design_master"

    id_sub_design = Column(Integer, primary_key=True, autoincrement=True)
    sub_designname = Column(String(100), nullable=False, unique=True)
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())
    date_upd = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    sub_design_maps = relationship("SubDesignMap", back_populates="sub_design")


class DesignMap(Base):
    """Design mapping table - matches 'design_map' sheet in Excel"""
    __tablename__ = "design_product_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    id_product = Column(Integer, ForeignKey('products.id_product'), nullable=False)
    product_name = Column(String(100))
    id_design = Column(Integer, ForeignKey('ret_design_master.id_design'), nullable=False)
    design_name = Column(String(100))
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())

    # Relationships
    product = relationship("Product", back_populates="design_maps")
    design = relationship("Design", back_populates="design_maps")


class SubDesignMap(Base):
    """Sub Design mapping table - matches 'sub_design map' sheet in Excel"""
    __tablename__ = "sub_design_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    id_product = Column(Integer, ForeignKey('products.id_product'))
    product_name = Column(String(100))
    id_design = Column(Integer, ForeignKey('ret_design_master.id_design'), nullable=False)
    design_name = Column(String(100))
    id_sub_design = Column(Integer, ForeignKey('ret_sub_design_master.id_sub_design'), nullable=False)
    sub_design_name = Column(String(100))
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())

    # Relationships
    design = relationship("Design", back_populates="sub_design_maps")
    sub_design = relationship("SubDesign", back_populates="sub_design_maps")


class ProductSectionMap(Base):
    """Product Section mapping table - matches 'product_section map' sheet in Excel"""
    __tablename__ = "product_section_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    id_product = Column(Integer, ForeignKey('products.id_product'), nullable=False)
    product_name = Column(String(100))
    section_id = Column(Integer)
    section_name = Column(String(100))
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())

    # Relationships
    product = relationship("Product", back_populates="section_maps")


# Future tag-related models (for reference, not implemented yet)
class TagDetails(Base):
    """Tag details table - matches 'tag_details' sheet structure"""
    __tablename__ = "tag_details"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_number = Column(String(50), nullable=False, unique=True)
    # Additional fields will be defined when tag import structure is finalized
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())


class TagStoneDetails(Base):
    """Tag stone details table - matches 'tag_stone_details' sheet structure"""
    __tablename__ = "tag_stone_details"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_number = Column(String(50), ForeignKey('tag_details.tag_number'), nullable=False)
    # Additional fields will be defined when stone details structure is finalized
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())


class TagCharges(Base):
    """Tag charges table - matches 'tag_charges' sheet structure"""
    __tablename__ = "tag_charges"

    id = Column(Integer, primary_key=True, autoincrement=True)
    tag_number = Column(String(50), ForeignKey('tag_details.tag_number'), nullable=False)
    # Additional fields will be defined when charges structure is finalized
    active = Column(Boolean, default=True)
    date_add = Column(DateTime, default=func.now())
