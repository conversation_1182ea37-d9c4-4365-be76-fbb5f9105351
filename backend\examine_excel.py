#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to examine the Excel file structure for master data import
"""

import pandas as pd
import sys
from pathlib import Path

def examine_excel_file(file_path):
    """Examine the Excel file and report on its structure"""
    
    print(f"📊 Examining Excel file: {file_path}")
    print("=" * 60)
    
    try:
        # Read Excel file to get sheet names
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Found {len(sheet_names)} sheets:")
        for i, sheet in enumerate(sheet_names, 1):
            print(f"  {i}. {sheet}")
        
        print("\n" + "=" * 60)
        
        # Examine each sheet
        for sheet_name in sheet_names:
            print(f"\n📄 Sheet: '{sheet_name}'")
            print("-" * 40)
            
            try:
                # Read the sheet
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                print(f"   Rows: {len(df)}")
                print(f"   Columns: {len(df.columns)}")
                
                if len(df.columns) > 0:
                    print(f"   Column Headers:")
                    for i, col in enumerate(df.columns, 1):
                        print(f"     {i}. '{col}'")
                
                # Check if there's any sample data
                if len(df) > 0:
                    print(f"   ✅ Has {len(df)} rows of data")
                    # Show first row as example
                    print(f"   Sample data (first row):")
                    for col in df.columns:
                        value = df.iloc[0][col] if not pd.isna(df.iloc[0][col]) else "Empty"
                        print(f"     {col}: {value}")
                else:
                    print(f"   ℹ️  No sample data (headers only)")
                
                # Check for any comments or special formatting in headers
                print(f"   Header Analysis:")
                required_fields = []
                optional_fields = []
                format_hints = []
                
                for col in df.columns:
                    col_str = str(col)
                    if '*' in col_str or 'required' in col_str.lower():
                        required_fields.append(col_str)
                    elif 'optional' in col_str.lower():
                        optional_fields.append(col_str)
                    elif '(' in col_str and ')' in col_str:
                        format_hints.append(col_str)
                
                if required_fields:
                    print(f"     ✅ Required fields detected: {len(required_fields)}")
                    for field in required_fields:
                        print(f"       - {field}")
                
                if optional_fields:
                    print(f"     ℹ️  Optional fields detected: {len(optional_fields)}")
                    for field in optional_fields:
                        print(f"       - {field}")
                
                if format_hints:
                    print(f"     💡 Format hints detected: {len(format_hints)}")
                    for field in format_hints:
                        print(f"       - {field}")
                
            except Exception as e:
                print(f"   ❌ Error reading sheet '{sheet_name}': {str(e)}")
        
        print("\n" + "=" * 60)
        print("📋 SUMMARY:")
        print(f"   Total Sheets: {len(sheet_names)}")
        print(f"   Sheet Names: {', '.join(sheet_names)}")
        
        # Recommendations
        print("\n💡 RECOMMENDATIONS:")
        
        # Check if sheet names follow master data pattern
        expected_masters = ['branch', 'category', 'product', 'design', 'sub_design', 'metal']
        found_masters = []
        
        for sheet in sheet_names:
            sheet_lower = sheet.lower().replace('_', '').replace('-', '').replace(' ', '')
            for master in expected_masters:
                if master.replace('_', '') in sheet_lower:
                    found_masters.append(master)
                    break
        
        print(f"   ✅ Detected master types: {', '.join(found_masters)}")
        
        missing_masters = set(expected_masters) - set(found_masters)
        if missing_masters:
            print(f"   ⚠️  Potentially missing masters: {', '.join(missing_masters)}")
        
        print(f"   ✅ Structure looks good for master data import!")
        print(f"   ✅ Ready to implement field mapping logic")
        
    except Exception as e:
        print(f"❌ Error examining file: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    # Try multiple possible locations
    possible_paths = [
        "/app/assets/tag_import.xlsx",
        "/app/tag_import.xlsx", 
        "assets/tag_import.xlsx",
        "tag_import.xlsx"
    ]
    
    file_path = None
    for path in possible_paths:
        if Path(path).exists():
            file_path = Path(path)
            break
    
    if not file_path:
        print(f"❌ File not found in any of these locations:")
        for path in possible_paths:
            print(f"   - {path}")
        sys.exit(1)
    
    examine_excel_file(file_path)
