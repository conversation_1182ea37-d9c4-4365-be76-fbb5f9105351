from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.database.connection import get_db, test_connection
from app.config import settings

router = APIRouter()

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": "1.0.0"
    }

@router.get("/health/database")
async def database_health_check(db: Session = Depends(get_db)):
    """Database health check endpoint"""
    try:
        # Test database connection
        db.execute("SELECT 1")
        return {
            "status": "healthy",
            "database": "connected",
            "database_name": settings.database_name
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }

@router.get("/health/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check with system information"""
    try:
        # Test database connection
        db.execute("SELECT 1")
        db_status = "connected"
        db_error = None
    except Exception as e:
        db_status = "disconnected"
        db_error = str(e)
    
    return {
        "status": "healthy" if db_status == "connected" else "unhealthy",
        "app_name": settings.app_name,
        "version": "1.0.0",
        "database": {
            "status": db_status,
            "name": settings.database_name,
            "host": settings.database_host,
            "error": db_error
        },
        "settings": {
            "debug": settings.debug,
            "max_file_size": settings.max_file_size,
            "allowed_file_types": settings.allowed_file_types
        }
    }
