version: '3.8'

services:
  # MySQL Database (using your existing database)
  mysql:
    image: mysql:8.0
    container_name: tag_import_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: amman_new
      MYSQL_USER: tag_import
      MYSQL_PASSWORD: tag_import_pass
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./tag_import.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - tag_import_network

  # Redis for background tasks
  redis:
    image: redis:7-alpine
    container_name: tag_import_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tag_import_network

  # FastAPI Backend
  backend:
    build: ./backend
    container_name: tag_import_backend
    environment:
      DATABASE_HOST: mysql
      DATABASE_PORT: 3306
      DATABASE_USER: root
      DATABASE_PASSWORD: password
      DATABASE_NAME: amman_new
      REDIS_URL: redis://redis:6379/0
      DEBUG: "true"
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - tag_import_network
    restart: unless-stopped

  # Next.js Frontend
  frontend:
    build: ./frontend
    container_name: tag_import_frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000/api/v1
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - tag_import_network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  backend_uploads:

networks:
  tag_import_network:
    driver: bridge
