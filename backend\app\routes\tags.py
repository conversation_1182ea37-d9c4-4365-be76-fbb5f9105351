from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import json

from app.database.connection import get_db
from app.models.pydantic_models import (
    TagResponse, BranchResponse, CategoryResponse, ProductResponse, 
    DesignResponse, SubDesignResponse, ImportBatchResponse, 
    ImportErrorResponse, ImportProgressResponse
)
from app.models.database_models import (
    Tag, Branch, Category, Product, Design, SubDesign,
    TagImportBatch, TagImportError
)
from app.services.tag_importer import TagImporter

router = APIRouter()

# Reference data endpoints
@router.get("/branches", response_model=List[BranchResponse])
async def get_branches(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get all branches"""
    query = db.query(Branch)
    if active_only:
        query = query.filter(Branch.active == True)
    return query.all()

@router.get("/categories", response_model=List[CategoryResponse])
async def get_categories(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get all categories"""
    query = db.query(Category)
    if active_only:
        query = query.filter(Category.active == True)
    return query.all()

@router.get("/products", response_model=List[ProductResponse])
async def get_products(
    category_id: Optional[int] = Query(None),
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get products, optionally filtered by category"""
    query = db.query(Product)
    if active_only:
        query = query.filter(Product.active == True)
    if category_id:
        query = query.filter(Product.id_category == category_id)
    return query.all()

@router.get("/designs", response_model=List[DesignResponse])
async def get_designs(
    product_id: Optional[int] = Query(None),
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get designs, optionally filtered by product"""
    query = db.query(Design)
    if active_only:
        query = query.filter(Design.active == True)
    if product_id:
        query = query.filter(Design.id_product == product_id)
    return query.all()

@router.get("/sub-designs", response_model=List[SubDesignResponse])
async def get_sub_designs(
    design_id: Optional[int] = Query(None),
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """Get sub-designs, optionally filtered by design"""
    query = db.query(SubDesign)
    if active_only:
        query = query.filter(SubDesign.active == True)
    if design_id:
        query = query.filter(SubDesign.id_design == design_id)
    return query.all()

# Tag endpoints
@router.get("/tags", response_model=List[TagResponse])
async def get_tags(
    branch_id: Optional[int] = Query(None),
    category_id: Optional[int] = Query(None),
    status: Optional[int] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """Get tags with optional filtering"""
    query = db.query(Tag)
    
    if branch_id:
        query = query.filter(Tag.id_branch == branch_id)
    if category_id:
        query = query.filter(Tag.id_category == category_id)
    if status:
        query = query.filter(Tag.status == status)
    
    return query.offset(offset).limit(limit).all()

@router.get("/tags/{tag_id}", response_model=TagResponse)
async def get_tag(tag_id: int, db: Session = Depends(get_db)):
    """Get a specific tag by ID"""
    tag = db.query(Tag).filter(Tag.id_tag == tag_id).first()
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    return tag

@router.get("/tags/by-number/{tag_no}", response_model=TagResponse)
async def get_tag_by_number(tag_no: str, db: Session = Depends(get_db)):
    """Get a specific tag by tag number"""
    tag = db.query(Tag).filter(Tag.tag_no == tag_no).first()
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    return tag

# Import batch endpoints
@router.get("/import-batches", response_model=List[ImportBatchResponse])
async def get_import_batches(
    status: Optional[str] = Query(None),
    limit: int = Query(50, le=100),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """Get import batches"""
    query = db.query(TagImportBatch)
    if status:
        query = query.filter(TagImportBatch.status == status)
    
    return query.order_by(TagImportBatch.created_at.desc()).offset(offset).limit(limit).all()

@router.get("/import-batches/{batch_id}", response_model=ImportBatchResponse)
async def get_import_batch(batch_id: int, db: Session = Depends(get_db)):
    """Get a specific import batch"""
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Import batch not found")
    return batch

@router.get("/import-batches/{batch_id}/errors", response_model=List[ImportErrorResponse])
async def get_import_errors(
    batch_id: int,
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """Get import errors for a specific batch"""
    # Check if batch exists
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Import batch not found")
    
    errors = db.query(TagImportError).filter(
        TagImportError.id_batch == batch_id
    ).offset(offset).limit(limit).all()
    
    return errors

@router.post("/import-batches/{batch_id}/start")
async def start_import(
    batch_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Start the import process for a batch"""
    # Check if batch exists
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Import batch not found")
    
    if batch.status != "PENDING":
        raise HTTPException(status_code=400, detail=f"Batch is not in PENDING status. Current status: {batch.status}")
    
    # Start import in background
    importer = TagImporter(db)
    background_tasks.add_task(importer.process_batch, batch_id)
    
    # Update batch status
    batch.status = "PROCESSING"
    db.commit()
    
    return {"message": "Import started", "batch_id": batch_id}

@router.get("/import-batches/{batch_id}/progress", response_model=ImportProgressResponse)
async def get_import_progress(batch_id: int, db: Session = Depends(get_db)):
    """Get import progress for a batch"""
    batch = db.query(TagImportBatch).filter(TagImportBatch.id_batch == batch_id).first()
    if not batch:
        raise HTTPException(status_code=404, detail="Import batch not found")
    
    progress_percentage = 0
    if batch.total_rows > 0:
        progress_percentage = (batch.processed_rows / batch.total_rows) * 100
    
    return ImportProgressResponse(
        batch_id=batch.id_batch,
        status=batch.status,
        total_rows=batch.total_rows,
        processed_rows=batch.processed_rows,
        success_rows=batch.success_rows,
        error_rows=batch.error_rows,
        progress_percentage=progress_percentage
    )
