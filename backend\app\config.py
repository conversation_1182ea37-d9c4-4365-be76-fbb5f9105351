from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # Database settings
    database_url: str = "mysql+pymysql://root:password@localhost:3306/amman_new"
    database_host: str = "localhost"
    database_port: int = 3306
    database_user: str = "root"
    database_password: str = "password"
    database_name: str = "amman_new"
    
    # Application settings
    app_name: str = "Jewelry Tag Import API"
    debug: bool = True
    
    # File upload settings
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_file_types: list = [".xlsx", ".xls", ".csv"]
    upload_dir: str = "uploads"
    
    # Redis settings (for background tasks)
    redis_url: str = "redis://localhost:6379/0"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    @property
    def database_url_sync(self) -> str:
        return f"mysql+pymysql://{self.database_user}:{self.database_password}@{self.database_host}:{self.database_port}/{self.database_name}"

settings = Settings()
