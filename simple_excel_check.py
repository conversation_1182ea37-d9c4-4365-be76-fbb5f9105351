#!/usr/bin/env python3
"""
Simple Excel file examination without pandas
"""

try:
    from openpyxl import load_workbook
    import sys
    from pathlib import Path
    
    file_path = Path("assets/tag_import.xlsx")
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    print("📊 Excel File Analysis")
    print("=" * 50)
    
    # Load workbook
    wb = load_workbook(file_path, data_only=True)
    sheet_names = wb.sheetnames
    
    print(f"Found {len(sheet_names)} sheets:")
    for i, sheet in enumerate(sheet_names, 1):
        print(f"  {i}. {sheet}")
    
    print("\n" + "=" * 50)
    
    # Examine each sheet
    for sheet_name in sheet_names:
        print(f"\n📄 Sheet: '{sheet_name}'")
        print("-" * 30)
        
        ws = wb[sheet_name]
        
        # Get dimensions
        max_row = ws.max_row
        max_col = ws.max_column
        
        print(f"   Dimensions: {max_row} rows x {max_col} columns")
        
        # Get headers (first row)
        headers = []
        for col in range(1, max_col + 1):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value:
                headers.append(str(cell_value))
        
        print(f"   Column Headers ({len(headers)}):")
        for i, header in enumerate(headers, 1):
            print(f"     {i}. '{header}'")
        
        # Check for data rows
        data_rows = max_row - 1  # Subtract header row
        if data_rows > 0:
            print(f"   ✅ Has {data_rows} rows of sample data")
            
            # Show first data row as example
            if data_rows > 0:
                print(f"   Sample data (row 2):")
                for col, header in enumerate(headers, 1):
                    cell_value = ws.cell(row=2, column=col).value
                    value_str = str(cell_value) if cell_value is not None else "Empty"
                    print(f"     {header}: {value_str}")
        else:
            print(f"   ℹ️  No sample data (headers only)")
        
        # Analyze headers for patterns
        required_fields = [h for h in headers if '*' in h or 'required' in h.lower()]
        optional_fields = [h for h in headers if 'optional' in h.lower()]
        format_hints = [h for h in headers if '(' in h and ')' in h]
        
        if required_fields:
            print(f"   ✅ Required fields detected: {len(required_fields)}")
            for field in required_fields:
                print(f"       - {field}")
        
        if optional_fields:
            print(f"   ℹ️  Optional fields detected: {len(optional_fields)}")
            for field in optional_fields:
                print(f"       - {field}")
        
        if format_hints:
            print(f"   💡 Format hints detected: {len(format_hints)}")
            for field in format_hints:
                print(f"       - {field}")
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print(f"   Total Sheets: {len(sheet_names)}")
    print(f"   Sheet Names: {', '.join(sheet_names)}")
    
    # Check for expected master types
    expected_masters = ['branch', 'category', 'product', 'design', 'sub_design', 'metal']
    found_masters = []
    
    for sheet in sheet_names:
        sheet_lower = sheet.lower().replace('_', '').replace('-', '').replace(' ', '')
        for master in expected_masters:
            if master.replace('_', '') in sheet_lower:
                found_masters.append(master)
                break
    
    print(f"   ✅ Detected master types: {', '.join(found_masters)}")
    
    missing_masters = set(expected_masters) - set(found_masters)
    if missing_masters:
        print(f"   ⚠️  Potentially missing masters: {', '.join(missing_masters)}")
    
    print(f"   ✅ Structure looks good for master data import!")
    print(f"   ✅ Ready to implement field mapping logic")
    
    wb.close()

except ImportError:
    print("❌ openpyxl not available. Installing...")
    import subprocess
    subprocess.run([sys.executable, "-m", "pip", "install", "openpyxl"])
    print("✅ Please run the script again")
    
except Exception as e:
    print(f"❌ Error examining file: {str(e)}")
