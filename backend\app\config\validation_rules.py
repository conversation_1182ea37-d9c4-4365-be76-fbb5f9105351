"""
Validation rules for master data import
Business logic and data integrity rules for jewelry inventory
"""

from typing import Dict, List, Any, Optional
import re
from decimal import Decimal


class ValidationRule:
    """Base class for validation rules"""
    
    def __init__(self, field_name: str, error_message: str):
        self.field_name = field_name
        self.error_message = error_message
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        """Override in subclasses"""
        return True


class RequiredFieldRule(ValidationRule):
    """Validates that required fields are not empty"""
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        if value is None or str(value).strip() == '' or str(value).lower() == 'nan':
            return False
        return True


class MaxLengthRule(ValidationRule):
    """Validates maximum string length"""
    
    def __init__(self, field_name: str, max_length: int, error_message: str = None):
        self.max_length = max_length
        if not error_message:
            error_message = f"{field_name} cannot exceed {max_length} characters"
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        if value is None:
            return True
        return len(str(value)) <= self.max_length


class PatternRule(ValidationRule):
    """Validates field against regex pattern"""
    
    def __init__(self, field_name: str, pattern: str, error_message: str = None):
        self.pattern = re.compile(pattern)
        if not error_message:
            error_message = f"{field_name} format is invalid"
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        if value is None or str(value).strip() == '':
            return True
        return bool(self.pattern.match(str(value)))


class NumericRangeRule(ValidationRule):
    """Validates numeric values within range"""
    
    def __init__(self, field_name: str, min_value: float = None, max_value: float = None, error_message: str = None):
        self.min_value = min_value
        self.max_value = max_value
        if not error_message:
            error_message = f"{field_name} must be between {min_value} and {max_value}"
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        if value is None or str(value).strip() == '':
            return True
        
        try:
            num_value = float(value)
            if self.min_value is not None and num_value < self.min_value:
                return False
            if self.max_value is not None and num_value > self.max_value:
                return False
            return True
        except (ValueError, TypeError):
            return False


class BooleanRule(ValidationRule):
    """Validates boolean fields"""
    
    def __init__(self, field_name: str, error_message: str = None):
        if not error_message:
            error_message = f"{field_name} must be Y/N, Yes/No, True/False, or 1/0"
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any, row_data: Dict[str, Any] = None) -> bool:
        if value is None or str(value).strip() == '':
            return True
        
        valid_values = [
            True, False, 1, 0, '1', '0',
            'Y', 'N', 'y', 'n',
            'Yes', 'No', 'yes', 'no', 'YES', 'NO',
            'True', 'False', 'true', 'false', 'TRUE', 'FALSE'
        ]
        
        return value in valid_values


# Validation rules for each master type
VALIDATION_RULES: Dict[str, Dict[str, List[ValidationRule]]] = {
    
    "category": {
        "categoryname": [
            RequiredFieldRule("categoryname", "Category Name is required"),
            MaxLengthRule("categoryname", 100, "Category Name cannot exceed 100 characters")
        ],
        "hsn_code": [
            MaxLengthRule("hsn_code", 20, "HSN Code cannot exceed 20 characters"),
            PatternRule("hsn_code", r"^[0-9]{4,8}$", "HSN Code must be 4-8 digits")
        ],
        "short_code": [
            MaxLengthRule("short_code", 10, "Short Code cannot exceed 10 characters")
        ],
        "category_type": [
            MaxLengthRule("category_type", 50, "Category Type cannot exceed 50 characters")
        ],
        "is_multi_metal": [
            BooleanRule("is_multi_metal", "Is Multi Metal must be Y/N, Yes/No, True/False, or 1/0")
        ],
        "metal_id": [
            NumericRangeRule("metal_id", 1, 9999, "Metal ID must be between 1 and 9999")
        ],
        "metal_name": [
            MaxLengthRule("metal_name", 50, "Metal Name cannot exceed 50 characters")
        ],
        "tax_group_id": [
            NumericRangeRule("tax_group_id", 1, 9999, "Tax Group ID must be between 1 and 9999")
        ],
        "tax_group_name": [
            MaxLengthRule("tax_group_name", 100, "Tax Group Name cannot exceed 100 characters")
        ],
        "purity_id": [
            NumericRangeRule("purity_id", 1, 9999, "Purity ID must be between 1 and 9999")
        ],
        "purity_name": [
            MaxLengthRule("purity_name", 50, "Purity Name cannot exceed 50 characters")
        ]
    },
    
    "product": {
        "productname": [
            RequiredFieldRule("productname", "Product Name is required"),
            MaxLengthRule("productname", 100, "Product Name cannot exceed 100 characters")
        ],
        "short_code": [
            MaxLengthRule("short_code", 10, "Short Code cannot exceed 10 characters")
        ],
        "hsn_code": [
            MaxLengthRule("hsn_code", 20, "HSN Code cannot exceed 20 characters"),
            PatternRule("hsn_code", r"^[0-9]{4,8}$", "HSN Code must be 4-8 digits")
        ],
        "id_category": [
            RequiredFieldRule("id_category", "Category ID is required"),
            NumericRangeRule("id_category", 1, 99999, "Category ID must be a valid number")
        ],
        "category_name": [
            MaxLengthRule("category_name", 100, "Category Name cannot exceed 100 characters")
        ],
        "no_of_pieces": [
            NumericRangeRule("no_of_pieces", 1, 1000, "Number of Pieces must be between 1 and 1000")
        ],
        "stock_type": [
            MaxLengthRule("stock_type", 50, "Stock Type cannot exceed 50 characters")
        ],
        "product_type": [
            MaxLengthRule("product_type", 50, "Product Type cannot exceed 50 characters")
        ]
    },
    
    "design": {
        "designname": [
            RequiredFieldRule("designname", "Design Name is required"),
            MaxLengthRule("designname", 100, "Design Name cannot exceed 100 characters")
        ]
    },
    
    "sub_design": {
        "sub_designname": [
            RequiredFieldRule("sub_designname", "Sub Design Name is required"),
            MaxLengthRule("sub_designname", 100, "Sub Design Name cannot exceed 100 characters")
        ]
    },
    
    "design_map": {
        "id_product": [
            RequiredFieldRule("id_product", "Product ID is required"),
            NumericRangeRule("id_product", 1, 99999, "Product ID must be a valid number")
        ],
        "id_design": [
            RequiredFieldRule("id_design", "Design ID is required"),
            NumericRangeRule("id_design", 1, 99999, "Design ID must be a valid number")
        ]
    },
    
    "sub_design map": {
        "id_design": [
            RequiredFieldRule("id_design", "Design ID is required"),
            NumericRangeRule("id_design", 1, 99999, "Design ID must be a valid number")
        ],
        "id_sub_design": [
            RequiredFieldRule("id_sub_design", "Sub Design ID is required"),
            NumericRangeRule("id_sub_design", 1, 99999, "Sub Design ID must be a valid number")
        ]
    },
    
    "product_section map": {
        "id_product": [
            RequiredFieldRule("id_product", "Product ID is required"),
            NumericRangeRule("id_product", 1, 99999, "Product ID must be a valid number")
        ],
        "section_id": [
            RequiredFieldRule("section_id", "Section ID is required"),
            NumericRangeRule("section_id", 1, 99999, "Section ID must be a valid number")
        ]
    }
}


def get_validation_rules(sheet_name: str) -> Dict[str, List[ValidationRule]]:
    """Get validation rules for a specific sheet"""
    return VALIDATION_RULES.get(sheet_name.lower(), {})


def validate_field(field_name: str, value: Any, sheet_name: str, row_data: Dict[str, Any] = None) -> List[str]:
    """Validate a single field value and return list of error messages"""
    rules = get_validation_rules(sheet_name)
    field_rules = rules.get(field_name, [])
    
    errors = []
    for rule in field_rules:
        if not rule.validate(value, row_data):
            errors.append(rule.error_message)
    
    return errors


def validate_row(row_data: Dict[str, Any], sheet_name: str) -> Dict[str, List[str]]:
    """Validate an entire row and return field-wise error messages"""
    errors = {}
    
    for field_name, value in row_data.items():
        field_errors = validate_field(field_name, value, sheet_name, row_data)
        if field_errors:
            errors[field_name] = field_errors
    
    return errors
